{"name": "backend", "version": "1.0.0", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "npm run db:generate", "postinstall": "npm run db:generate", "deploy": "npm run db:migrate && npm run db:generate", "deploy:setup": "node scripts/deploy-setup.js", "db:migrate": "prisma migrate deploy", "db:generate": "prisma generate --no-engine", "db:reset": "prisma migrate reset --force", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["cms", "api", "nodejs", "express"], "author": "", "license": "ISC", "description": "Backend API for dynamic website with CMS", "dependencies": {"@prisma/client": "^6.14.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.14.3", "prisma": "^6.14.0"}, "devDependencies": {"@types/node": "^24.2.1", "nodemon": "^3.1.10"}}